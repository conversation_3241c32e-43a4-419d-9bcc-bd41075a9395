<template>
    <div class="group-overview">
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="待审核临时项目" name="toBeReviewed">
                <review-list></review-list>
            </el-tab-pane>
            <el-tab-pane label="待确认正式项目" name="toBeConfirmed">
                <confirm-list></confirm-list>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import ReviewList from './reviewList/index.vue';
import ConfirmList from './confirmList/index.vue';

export default {
    name: 'Review',
    components: {
        ReviewList,
        ConfirmList
    },
    props: {},
    data() {
        return {
            activeName: 'toBeReviewed',
            group: '',
            groupListOptions: []
        };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
.group-overview {
    position: relative;
    padding: 10px 20px;
    ::v-deep .el-tabs__nav-wrap {
        display: flex;
        justify-content: center;
        .el-tabs__item {
            color: #4377ee;
        }
        .el-tabs__item.is-active {
            background-color: #4377ee;
            color: #fff;
        }
    }
    ::v-deep .el-tabs--card > .el-tabs__header {
        border-bottom: 1px solid #4377ee;
    }
}
.group-list {
    position: absolute;
    display: flex;
    align-items: center;
    .group-list-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-right: 15px;
    }
}
</style>
